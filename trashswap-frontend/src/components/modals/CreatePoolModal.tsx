'use client';

import React, { useState, useEffect, useRef } from 'react';
import { PublicKey } from '@solana/web3.js';

interface CreatePoolModalProps {
  isOpen: boolean;
  onClose: () => void;
  onPoolCreated: () => void;
}

interface TokenInfo {
  mint: string;
  symbol: string;
  name: string;
  decimals: number;
}

// Common tokens on Gorbagana network
const COMMON_TOKENS: TokenInfo[] = [
  {
    mint: 'GorBaGaNaTokenMintAddress1111111111111111111',
    symbol: 'GOR',
    name: 'Gorbagana Token',
    decimals: 9
  },
  {
    mint: 'TUSDCTokenMintAddress1111111111111111111111',
    symbol: 'TUSDC',
    name: 'Test USDC',
    decimals: 6
  }
];

export default function CreatePoolModal({ isOpen, onClose, onPoolCreated }: CreatePoolModalProps) {
  const dialogRef = useRef<HTMLDialogElement | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    tokenX: '',
    tokenY: '',
    fee: 0.3,
    initialLiquidityX: '',
    initialLiquidityY: ''
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Handle modal open/close
  useEffect(() => {
    if (!dialogRef.current) return;
    
    if (isOpen) {
      dialogRef.current.showModal();
    } else {
      dialogRef.current.close();
    }
  }, [isOpen]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    // Validate token addresses
    if (!formData.tokenX) {
      newErrors.tokenX = 'Token X is required';
    } else {
      try {
        new PublicKey(formData.tokenX);
      } catch {
        newErrors.tokenX = 'Invalid token address';
      }
    }

    if (!formData.tokenY) {
      newErrors.tokenY = 'Token Y is required';
    } else {
      try {
        new PublicKey(formData.tokenY);
      } catch {
        newErrors.tokenY = 'Invalid token address';
      }
    }

    // Check if tokens are different
    if (formData.tokenX && formData.tokenY && formData.tokenX === formData.tokenY) {
      newErrors.tokenY = 'Tokens must be different';
    }

    // Validate fee
    if (formData.fee < 0.01 || formData.fee > 10) {
      newErrors.fee = 'Fee must be between 0.01% and 10%';
    }

    // Validate initial liquidity
    if (!formData.initialLiquidityX || parseFloat(formData.initialLiquidityX) <= 0) {
      newErrors.initialLiquidityX = 'Initial liquidity must be greater than 0';
    }

    if (!formData.initialLiquidityY || parseFloat(formData.initialLiquidityY) <= 0) {
      newErrors.initialLiquidityY = 'Initial liquidity must be greater than 0';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    
    try {
      // TODO: Implement actual pool creation logic
      console.log('Creating pool with data:', formData);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      onPoolCreated();
      onClose();
      
      // Reset form
      setFormData({
        tokenX: '',
        tokenY: '',
        fee: 0.3,
        initialLiquidityX: '',
        initialLiquidityY: ''
      });
      setErrors({});
      
    } catch (error) {
      console.error('Failed to create pool:', error);
      setErrors({ submit: 'Failed to create pool. Please try again.' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleTokenSelect = (field: 'tokenX' | 'tokenY', token: TokenInfo) => {
    setFormData(prev => ({
      ...prev,
      [field]: token.mint
    }));
    setErrors(prev => ({ ...prev, [field]: '' }));
  };

  const getTokenSymbol = (mint: string) => {
    const token = COMMON_TOKENS.find(t => t.mint === mint);
    return token?.symbol || 'Unknown';
  };

  return (
    <dialog ref={dialogRef} className="modal" onClose={onClose}>
      <div className="modal-box max-w-lg">
        <div className="flex items-center justify-between mb-6">
          <h3 className="font-bold text-lg">Create Liquidity Pool</h3>
          <button 
            className="btn btn-sm btn-circle btn-ghost"
            onClick={onClose}
            disabled={isLoading}
          >
            ✕
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Token X Selection */}
          <div>
            <label className="label">
              <span className="label-text font-medium">Token X</span>
            </label>
            <div className="space-y-2">
              <input
                type="text"
                placeholder="Enter token mint address"
                className={`input input-bordered w-full ${errors.tokenX ? 'input-error' : ''}`}
                value={formData.tokenX}
                onChange={(e) => {
                  setFormData(prev => ({ ...prev, tokenX: e.target.value }));
                  setErrors(prev => ({ ...prev, tokenX: '' }));
                }}
              />
              <div className="flex gap-2">
                {COMMON_TOKENS.map((token) => (
                  <button
                    key={token.mint}
                    type="button"
                    className={`btn btn-sm ${
                      formData.tokenX === token.mint ? 'btn-primary' : 'btn-outline'
                    }`}
                    onClick={() => handleTokenSelect('tokenX', token)}
                  >
                    {token.symbol}
                  </button>
                ))}
              </div>
              {errors.tokenX && (
                <div className="text-error text-sm">{errors.tokenX}</div>
              )}
            </div>
          </div>

          {/* Token Y Selection */}
          <div>
            <label className="label">
              <span className="label-text font-medium">Token Y</span>
            </label>
            <div className="space-y-2">
              <input
                type="text"
                placeholder="Enter token mint address"
                className={`input input-bordered w-full ${errors.tokenY ? 'input-error' : ''}`}
                value={formData.tokenY}
                onChange={(e) => {
                  setFormData(prev => ({ ...prev, tokenY: e.target.value }));
                  setErrors(prev => ({ ...prev, tokenY: '' }));
                }}
              />
              <div className="flex gap-2">
                {COMMON_TOKENS.map((token) => (
                  <button
                    key={token.mint}
                    type="button"
                    className={`btn btn-sm ${
                      formData.tokenY === token.mint ? 'btn-primary' : 'btn-outline'
                    }`}
                    onClick={() => handleTokenSelect('tokenY', token)}
                  >
                    {token.symbol}
                  </button>
                ))}
              </div>
              {errors.tokenY && (
                <div className="text-error text-sm">{errors.tokenY}</div>
              )}
            </div>
          </div>

          {/* Fee Selection */}
          <div>
            <label className="label">
              <span className="label-text font-medium">Fee Tier</span>
              <span className="label-text-alt">{formData.fee}%</span>
            </label>
            <div className="grid grid-cols-3 gap-2">
              {[0.05, 0.3, 1.0].map((fee) => (
                <button
                  key={fee}
                  type="button"
                  className={`btn btn-sm ${
                    formData.fee === fee ? 'btn-primary' : 'btn-outline'
                  }`}
                  onClick={() => setFormData(prev => ({ ...prev, fee }))}
                >
                  {fee}%
                </button>
              ))}
            </div>
            {errors.fee && (
              <div className="text-error text-sm mt-1">{errors.fee}</div>
            )}
          </div>

          {/* Initial Liquidity */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="label">
                <span className="label-text font-medium">
                  {getTokenSymbol(formData.tokenX)} Amount
                </span>
              </label>
              <input
                type="number"
                placeholder="0.0"
                className={`input input-bordered w-full ${errors.initialLiquidityX ? 'input-error' : ''}`}
                value={formData.initialLiquidityX}
                onChange={(e) => {
                  setFormData(prev => ({ ...prev, initialLiquidityX: e.target.value }));
                  setErrors(prev => ({ ...prev, initialLiquidityX: '' }));
                }}
                min="0"
                step="any"
              />
              {errors.initialLiquidityX && (
                <div className="text-error text-sm mt-1">{errors.initialLiquidityX}</div>
              )}
            </div>

            <div>
              <label className="label">
                <span className="label-text font-medium">
                  {getTokenSymbol(formData.tokenY)} Amount
                </span>
              </label>
              <input
                type="number"
                placeholder="0.0"
                className={`input input-bordered w-full ${errors.initialLiquidityY ? 'input-error' : ''}`}
                value={formData.initialLiquidityY}
                onChange={(e) => {
                  setFormData(prev => ({ ...prev, initialLiquidityY: e.target.value }));
                  setErrors(prev => ({ ...prev, initialLiquidityY: '' }));
                }}
                min="0"
                step="any"
              />
              {errors.initialLiquidityY && (
                <div className="text-error text-sm mt-1">{errors.initialLiquidityY}</div>
              )}
            </div>
          </div>

          {/* Submit Error */}
          {errors.submit && (
            <div className="alert alert-error">
              <span>{errors.submit}</span>
            </div>
          )}

          {/* Actions */}
          <div className="modal-action">
            <button 
              type="button" 
              className="btn btn-ghost" 
              onClick={onClose}
              disabled={isLoading}
            >
              Cancel
            </button>
            <button 
              type="submit" 
              className="btn btn-primary"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <span className="loading loading-spinner loading-sm"></span>
                  Creating...
                </>
              ) : (
                'Create Pool'
              )}
            </button>
          </div>
        </form>
      </div>
      
      {/* Backdrop */}
      <form method="dialog" className="modal-backdrop">
        <button onClick={onClose}>close</button>
      </form>
    </dialog>
  );
}
