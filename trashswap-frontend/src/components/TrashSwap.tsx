'use client';

import React from 'react';
import { PoolProvider, usePoolContext } from '../contexts/PoolContext';
import { UIProvider, useUIContext } from '../contexts/UIContext';
import MainLayout from './layout/MainLayout';
import SwapTokens from './SwapTokens';
import DepositLiquidity from './DepositLiquidity';
import SettingsModal from './modals/SettingsModal';
import CreatePoolModal from './modals/CreatePoolModal';

function TrashSwapContent() {
  const { pools, refreshPools } = usePoolContext();
  const {
    isSettingsModalOpen,
    setSettingsModalOpen,
    isCreatePoolModalOpen,
    setCreatePoolModalOpen,
    currentView,
    setCurrentView
  } = useUIContext();

  const handlePoolCreated = async () => {
    // Refresh pools list
    console.log('Pool created, refreshing list...');
    await refreshPools();
    setCreatePoolModalOpen(false);
  };

  const handleDeposit = async () => {
    console.log('Liquidity deposited, refreshing list...');
    await refreshPools();
  };

  return (
    <>
      <MainLayout
        onCreatePoolClick={() => setCreatePoolModalOpen(true)}
        onSettingsClick={() => setSettingsModalOpen(true)}
      >
        {/* Main Content Area - Focused on Swap Interface */}
        <div className="max-w-2xl mx-auto">
          {/* View Toggle */}
          <div className="mb-6">
            <div className="tabs tabs-boxed bg-base-200 p-1">
              <button
                className={`tab ${currentView === 'swap' ? 'tab-active' : ''}`}
                onClick={() => setCurrentView('swap')}
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />
                </svg>
                Swap Tokens
              </button>
              <button
                className={`tab ${currentView === 'deposit' ? 'tab-active' : ''}`}
                onClick={() => setCurrentView('deposit')}
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Add Liquidity
              </button>
            </div>
          </div>

          {/* Main Interface */}
          <div className="card bg-base-200 shadow-xl">
            <div className="card-body">
              {currentView === 'swap' && <SwapTokens pools={pools} />}
              {currentView === 'deposit' && <DepositLiquidity pools={pools} onDeposit={handleDeposit} />}
            </div>
          </div>

          {/* Network Info */}
          <div className="mt-6 text-center">
            <div className="flex items-center justify-center space-x-2 text-sm text-base-content/70">
              <div className="w-2 h-2 bg-success rounded-full"></div>
              <span>Connected to Gorbagana Network</span>
            </div>
            <div className="text-xs text-base-content/50 mt-1">
              RPC: https://rpc.gorbagana.wtf/
            </div>
          </div>
        </div>
      </MainLayout>

      {/* Modals */}
      <SettingsModal
        isOpen={isSettingsModalOpen}
        onClose={() => setSettingsModalOpen(false)}
      />

      <CreatePoolModal
        isOpen={isCreatePoolModalOpen}
        onClose={() => setCreatePoolModalOpen(false)}
        onPoolCreated={handlePoolCreated}
      />
    </>
  );
}

export default function TrashSwap() {
  return (
    <PoolProvider>
      <UIProvider>
        <TrashSwapContent />
      </UIProvider>
    </PoolProvider>
  );
}
