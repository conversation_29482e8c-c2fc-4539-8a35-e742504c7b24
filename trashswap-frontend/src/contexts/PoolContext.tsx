'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useConnection, useWallet } from '@solana/wallet-adapter-react';
import { PublicKey } from '@solana/web3.js';
import { Program, AnchorProvider, BN } from '@coral-xyz/anchor';
import idl from '../idl/trash_swap.json';

const PROGRAM_ID = new PublicKey('Cc3WZJGkQK9oz2bc1cPN3zcBSPMkmdbEvJfCyuLNhzKy');

// Define the Pool type
export interface Pool {
  config: PublicKey;
  mintX: PublicKey;
  mintY: PublicKey;
  mintLp: PublicKey;
  seed: BN;
  fee: number;
  locked: boolean;
  authority: PublicKey | null;
  tokenXSymbol: string;
  tokenYSymbol: string;
}

interface PoolContextType {
  pools: Pool[];
  loading: boolean;
  error: string | null;
  refreshPools: () => Promise<void>;
  addPool: (pool: Pool) => void;
}

const PoolContext = createContext<PoolContextType | undefined>(undefined);

export function usePoolContext() {
  const context = useContext(PoolContext);
  if (context === undefined) {
    throw new Error('usePoolContext must be used within a PoolProvider');
  }
  return context;
}

interface PoolProviderProps {
  children: ReactNode;
}

export function PoolProvider({ children }: PoolProviderProps) {
  const { connection } = useConnection();
  const wallet = useWallet();
  const [pools, setPools] = useState<Pool[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getProvider = () => {
    if (!wallet.publicKey || !wallet.signTransaction) return null;
    return new AnchorProvider(connection, wallet as any, { commitment: 'confirmed' });
  };



  const fetchPools = async () => {
    console.log('🔍 Starting to fetch pools...');
    setLoading(true);
    setError(null);

    try {
      const provider = getProvider();
      if (!provider) {
        console.log('❌ No provider - wallet not connected');
        setError('Wallet not connected');
        setLoading(false);
        return;
      }

      // Create program with our specific program ID
      const programWithId = { ...idl, address: PROGRAM_ID.toString() };
      const program = new Program(programWithId as any, provider);
      console.log('📋 Program initialized, fetching pool configs...');

      // For now, we'll manually add known pools since fetching all Config accounts has issues
      // In a production app, you'd maintain a registry of pools or use getProgramAccounts
      const configAccounts: any[] = [];

      // Try to find pools by deriving config addresses for known token pairs
      const knownTokenPairs = [
        {
          mintX: 'DBr7v2JVLX1YU3cerMrHxxavapezWsNYTb7ExCNRZPLQ', // WGOR
          mintY: '2Ley4yFr6fP3kC2DV5w2foU6zXWgdGUUQ9hFmYNojwKZ', // TUSDC
        }
      ];

      for (const pair of knownTokenPairs) {
        for (let seedValue = 0; seedValue < 10; seedValue++) {
          try {
            const [configAddress] = PublicKey.findProgramAddressSync(
              [
                Buffer.from('config'),
                new PublicKey(pair.mintX).toBuffer(),
                new PublicKey(pair.mintY).toBuffer(),
                new BN(seedValue).toArrayLike(Buffer, 'le', 8)
              ],
              PROGRAM_ID
            );

            console.log(`� Checking config at seed ${seedValue}:`, configAddress.toString());

            // Try to fetch the raw account data first
            const accountInfo = await provider.connection.getAccountInfo(configAddress);
            if (!accountInfo) {
              continue; // Try next seed
            }

            console.log('✅ Raw account found:', {
              owner: accountInfo.owner.toString(),
              dataLength: accountInfo.data.length,
              executable: accountInfo.executable
            });

            // Check if the account is owned by our program
            if (!accountInfo.owner.equals(PROGRAM_ID)) {
              throw new Error(`Account is owned by ${accountInfo.owner.toString()}, expected ${PROGRAM_ID.toString()}`);
            }

            // Try to decode using the program if possible
            let configAccount;
            if (program.account && (program.account as any).config) {
              configAccount = await (program.account as any).config.fetch(configAddress);
            } else {
              // Manually decode the account data
              // For now, just create a mock structure to show the pool exists
              configAccount = {
                seed: seedValue,
                mintX: pair.mintX,
                mintY: pair.mintY,
                fee: 30,
                locked: false,
                authority: null
              };
            }

            console.log('✅ Successfully fetched pool config:', configAccount);
            configAccounts.push({
              publicKey: configAddress,
              account: configAccount
            });
          } catch (seedError) {
            console.error('❌ Could not fetch known pool config:', seedError);
          }
        }
      }

      const poolsData: Pool[] = [];

      for (const account of configAccounts) {
        const config = account.account as any;

        // Get token symbols with proper names for known tokens
        const getTokenSymbolSync = (mint: string) => {
          if (mint === 'DBr7v2JVLX1YU3cerMrHxxavapezWsNYTb7ExCNRZPLQ') return 'WGOR';
          if (mint === '2Ley4yFr6fP3kC2DV5w2foU6zXWgdGUUQ9hFmYNojwKZ') return 'TUSDC';
          return `${mint.slice(0, 4)}...${mint.slice(-4)}`;
        };

        const tokenXSymbol = getTokenSymbolSync(config.mintX.toString());
        const tokenYSymbol = getTokenSymbolSync(config.mintY.toString());

        poolsData.push({
          config: account.publicKey,
          mintX: new PublicKey(config.mintX),
          mintY: new PublicKey(config.mintY),
          mintLp: new PublicKey('11111111111111111111111111111111'), // Placeholder
          seed: new BN(config.seed),
          fee: config.fee,
          locked: config.locked,
          authority: config.authority,
          tokenXSymbol,
          tokenYSymbol
        });

        console.log(`✅ Added pool: ${tokenXSymbol}/${tokenYSymbol}`);
      }

      console.log(`🎉 Successfully loaded ${poolsData.length} pools`);
      setPools(poolsData);
      setError(null);

    } catch (err) {
      console.error('❌ Error fetching pools:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch pools');
      setPools([]);
    } finally {
      setLoading(false);
    }
  };

  const refreshPools = async () => {
    await fetchPools();
  };

  const addPool = (pool: Pool) => {
    setPools(prev => [...prev, pool]);
  };

  // Auto-fetch pools when wallet connects
  useEffect(() => {
    if (wallet.publicKey) {
      fetchPools();
    } else {
      setPools([]);
      setError(null);
    }
  }, [wallet.publicKey, connection]);

  // Auto-refresh pools every 30 seconds when wallet is connected
  useEffect(() => {
    if (!wallet.publicKey) return;

    const interval = setInterval(() => {
      fetchPools();
    }, 30000);

    return () => clearInterval(interval);
  }, [wallet.publicKey]);

  const value: PoolContextType = {
    pools,
    loading,
    error,
    refreshPools,
    addPool
  };

  return (
    <PoolContext.Provider value={value}>
      {children}
    </PoolContext.Provider>
  );
}
