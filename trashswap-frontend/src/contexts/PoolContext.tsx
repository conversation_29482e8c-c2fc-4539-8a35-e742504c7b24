'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useConnection, useWallet } from '@solana/wallet-adapter-react';
import { PublicKey } from '@solana/web3.js';
import { Program, AnchorProvider, BN } from '@coral-xyz/anchor';
import idl from '../idl/trash_swap.json';

const PROGRAM_ID = new PublicKey('Cc3WZJGkQK9oz2bc1cPN3zcBSPMkmdbEvJfCyuLNhzKy');

// Define the Pool type
export interface Pool {
  config: PublicKey;
  mintX: PublicKey;
  mintY: PublicKey;
  mintLp: PublicKey;
  seed: BN;
  fee: number;
  locked: boolean;
  authority: PublicKey | null;
  tokenXSymbol: string;
  tokenYSymbol: string;
}

interface PoolContextType {
  pools: Pool[];
  loading: boolean;
  error: string | null;
  refreshPools: () => Promise<void>;
  addPool: (pool: Pool) => void;
}

const PoolContext = createContext<PoolContextType | undefined>(undefined);

export function usePoolContext() {
  const context = useContext(PoolContext);
  if (context === undefined) {
    throw new Error('usePoolContext must be used within a PoolProvider');
  }
  return context;
}

interface PoolProviderProps {
  children: ReactNode;
}

export function PoolProvider({ children }: PoolProviderProps) {
  const { connection } = useConnection();
  const wallet = useWallet();
  const [pools, setPools] = useState<Pool[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getProvider = () => {
    if (!wallet.publicKey || !wallet.signTransaction) return null;
    return new AnchorProvider(connection, wallet as any, { commitment: 'confirmed' });
  };

  const getTokenSymbol = async (mint: PublicKey): Promise<string> => {
    // This is a simplified version - in a real app, you'd want to fetch metadata
    const mintStr = mint.toString();
    
    // Known token mappings for Gorbagana network
    const knownTokens: Record<string, string> = {
      'GorBaGaNaTokenMintAddress1111111111111111111': 'GOR',
      'TUSDCTokenMintAddress1111111111111111111111': 'TUSDC',
      // Add more known tokens as needed
    };

    return knownTokens[mintStr] || `${mintStr.slice(0, 4)}...${mintStr.slice(-4)}`;
  };

  const fetchPools = async () => {
    console.log('🔍 Starting to fetch pools...');
    setLoading(true);
    setError(null);

    try {
      const provider = getProvider();
      if (!provider) {
        console.log('❌ No provider - wallet not connected');
        setError('Wallet not connected');
        setLoading(false);
        return;
      }

      const program = new Program(idl as any, PROGRAM_ID, provider);
      console.log('📋 Program initialized, fetching pool configs...');

      // Fetch all pool config accounts
      const poolConfigs = await program.account.poolConfig.all();
      console.log(`📊 Found ${poolConfigs.length} pool configs`);

      if (poolConfigs.length === 0) {
        console.log('ℹ️ No pools found');
        setPools([]);
        setLoading(false);
        return;
      }

      // Process each pool config
      const poolsData: Pool[] = [];
      
      for (const poolConfig of poolConfigs) {
        try {
          const config = poolConfig.account as any;
          console.log('🏊 Processing pool:', {
            publicKey: poolConfig.publicKey.toString(),
            mintX: config.mintX.toString(),
            mintY: config.mintY.toString(),
            fee: config.fee,
            locked: config.locked
          });

          // Get token symbols
          const tokenXSymbol = await getTokenSymbol(config.mintX);
          const tokenYSymbol = await getTokenSymbol(config.mintY);

          const pool: Pool = {
            config: poolConfig.publicKey,
            mintX: config.mintX,
            mintY: config.mintY,
            mintLp: config.mintLp,
            seed: config.seed,
            fee: config.fee,
            locked: config.locked,
            authority: config.authority,
            tokenXSymbol,
            tokenYSymbol
          };

          poolsData.push(pool);
          console.log(`✅ Added pool: ${tokenXSymbol}/${tokenYSymbol}`);
        } catch (poolError) {
          console.error('❌ Error processing pool:', poolError);
        }
      }

      console.log(`🎉 Successfully loaded ${poolsData.length} pools`);
      setPools(poolsData);
      setError(null);

    } catch (err) {
      console.error('❌ Error fetching pools:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch pools');
      setPools([]);
    } finally {
      setLoading(false);
    }
  };

  const refreshPools = async () => {
    await fetchPools();
  };

  const addPool = (pool: Pool) => {
    setPools(prev => [...prev, pool]);
  };

  // Auto-fetch pools when wallet connects
  useEffect(() => {
    if (wallet.publicKey) {
      fetchPools();
    } else {
      setPools([]);
      setError(null);
    }
  }, [wallet.publicKey, connection]);

  // Auto-refresh pools every 30 seconds when wallet is connected
  useEffect(() => {
    if (!wallet.publicKey) return;

    const interval = setInterval(() => {
      fetchPools();
    }, 30000);

    return () => clearInterval(interval);
  }, [wallet.publicKey]);

  const value: PoolContextType = {
    pools,
    loading,
    error,
    refreshPools,
    addPool
  };

  return (
    <PoolContext.Provider value={value}>
      {children}
    </PoolContext.Provider>
  );
}
