@import "tailwindcss";

/* Ensure no purple background shows through */
html, body {
  background-color: #1a1a1a !important; /* Dark background matching base-100 */
  margin: 0;
  padding: 0;
  height: 100%;
}

/* Custom DaisyUI theme - sunset */
[data-theme="sunset"] {
  color-scheme: dark;
  --b1: 22% 0.019 237.69; /* base-100 */
  --b2: 20% 0.019 237.69; /* base-200 */
  --b3: 18% 0.019 237.69; /* base-300 */
  --bc: 77.383% 0.043 245.096; /* base-content */
  --p: 74.703% 0.158 39.947; /* primary */
  --pc: 14.94% 0.031 39.947; /* primary-content */
  --s: 72.537% 0.177 2.72; /* secondary */
  --sc: 14.507% 0.035 2.72; /* secondary-content */
  --a: 71.294% 0.166 299.844; /* accent */
  --ac: 14.258% 0.033 299.844; /* accent-content */
  --n: 26% 0.019 237.69; /* neutral */
  --nc: 70% 0.019 237.69; /* neutral-content */
  --in: 85.559% 0.085 206.015; /* info */
  --inc: 17.111% 0.017 206.015; /* info-content */
  --su: 85.56% 0.085 144.778; /* success */
  --suc: 17.112% 0.017 144.778; /* success-content */
  --wa: 85.569% 0.084 74.427; /* warning */
  --wac: 17.113% 0.016 74.427; /* warning-content */
  --er: 85.511% 0.078 16.886; /* error */
  --erc: 17.102% 0.015 16.886; /* error-content */
  --rounded-box: 1rem;
  --rounded-btn: 0.5rem;
  --rounded-badge: 1.9rem;
  --animation-btn: 0.25s;
  --animation-input: 0.2s;
  --btn-focus-scale: 0.95;
  --border-btn: 1px;
  --tab-border: 1px;
  --tab-radius: 0.5rem;
}


