# TrashSwap UI Restructure Task

## Project Overview
TrashSwap is a decentralized exchange (DEX) built for the Gorbagana network using automated market making (AMM) functionality. This task involves restructuring the current layout to match a modern, sleek interface similar to professional DEX platforms.

### Current Architecture
- Next.js application with TypeScript
- Solana/Anchor integration for blockchain interactions
- Component-based structure with separate swap, pool creation, and liquidity management features
- Currently uses a tab-based layout that needs to be converted to a sidebar + main content layout

## Design Requirements

### 1. Overall Layout Structure
```
┌─────────────────────────────────────────────────────────────┐
│  Header: Logo + Wallet Connect + Settings + Network        │
├─────────────────────────────────────────────────────────────┤
│  ┌───────────┐ ┌─────────────────────────────────────────┐  │
│  │           │ │                                         │  │
│  │  Sidebar  │ │         Main Content Area               │  │
│  │   (Pools) │ │         (Swap Interface)                │  │
│  │           │ │                                         │  │
│  │           │ │                                         │  │
│  └───────────┘ └─────────────────────────────────────────┘  │
├─────────────────────────────────────────────────────────────┤
│  Footer: Social Links (Twitter, Discord, GitHub, Docs)     │
└─────────────────────────────────────────────────────────────┘
```

### 2. Header Components
- **Logo**: "🗑️ TrashSwap" with tagline "A Trashy DEX on Gorbagana Network"
- **Wallet Connect Button**: Sleek button showing connected wallet address (truncated) or "Connect Wallet"
- **Settings Icon**: Gear icon that opens settings modal
- **Network Selector**: Dropdown showing "Gorbagana Network" with network icon

### 3. Sidebar (Left Panel)
- **Width**: ~300px, collapsible on mobile
- **Content**: Pool list with search functionality
- **Features**:
  - Search bar for filtering pools
  - Pool cards showing:
    - Token pair (e.g., "GOR/TUSDC")
    - TVL (Total Value Locked)
    - 24h Volume
    - Fee tier
  - "Create Pool" button at the bottom

### 4. Main Content Area (Center)
- **Primary Focus**: Token swap interface
- **Features**:
  - Large, prominent swap interface
  - Token selection dropdowns
  - Amount input fields
  - Swap button
  - Slippage settings
  - Transaction summary

### 5. Footer
- **Position**: Fixed at bottom right
- **Content**: Social media icons (Twitter, Discord, GitHub, Documentation)
- **Style**: Subtle, non-intrusive

## Technical Implementation Tasks

### Task 1: Create New Layout Components

#### 1.1 Create Header Component (`src/components/layout/Header.tsx`)
```typescript
// Features needed:
- Logo with brand name
- Wallet connection button with Web3 integration
- Settings modal trigger
- Network selector dropdown
- Responsive design for mobile
```

#### 1.2 Create Sidebar Component (`src/components/layout/Sidebar.tsx`)
```typescript
// Features needed:
- Collapsible sidebar for mobile
- Pool search functionality
- Pool list display with real-time data
- Create pool navigation
- Smooth animations
```

#### 1.3 Create Footer Component (`src/components/layout/Footer.tsx`)
```typescript
// Features needed:
- Social media links
- Fixed positioning
- Hover effects
- Icon integration (Twitter, Discord, GitHub, Docs)
```

### Task 2: Restructure Main Layout

#### 2.1 Update `src/components/TrashSwap.tsx`
- Remove tab-based navigation
- Implement new layout structure
- Focus main area on swap functionality
- Integrate sidebar for pool management

#### 2.2 Create Layout Wrapper (`src/components/layout/MainLayout.tsx`)
- Combine Header, Sidebar, Main Content, and Footer
- Handle responsive behavior
- Manage layout state (sidebar collapse, etc.)

### Task 3: Update Individual Components

#### 3.1 Enhance `SwapTokens.tsx`
- Make it the primary focus of the main content area
- Improve visual hierarchy
- Add better token selection UI
- Implement slippage controls
- Add transaction preview

#### 3.2 Modify `PoolList.tsx`
- Adapt for sidebar display
- Add search/filter functionality
- Improve pool card design
- Add loading states and error handling

#### 3.3 Update `CreatePool.tsx`
- Convert to modal or dedicated page
- Integrate with sidebar navigation
- Improve form design and validation

### Task 4: Styling and Theme

#### 4.1 Color Scheme following custom DaisyUi them in Globals.css
- **Primary**: Dark theme with daisyUI accents
- **Background**: Dark grey/black gradient
- **Cards**: Dark gray with subtle borders
- **Text**: White primary, gray secondary
- **Accent**: follow the DaisyUi theme for interactive elements

#### 4.2 Typography
- **Headers**: Bold, modern font
- **Body**: Clean, readable sans-serif
- **Numbers**: Monospace for better alignment

#### 4.3 Interactive Elements
- Smooth hover transitions
- Subtle shadows and glows
- Loading animations
- Success/error state indicators

### Task 5: Responsive Design

#### 5.1 Mobile Adaptations
- Collapsible sidebar that slides over content
- Hamburger menu for navigation
- Touch-friendly button sizes
- Optimized swap interface for mobile

#### 5.2 Tablet Adaptations
- Maintain sidebar but reduce width
- Adjust component spacing
- Ensure touch targets are appropriate

### Task 6: Integration Requirements

#### 6.1 Solana/Gorbagana Integration
- Maintain existing Web3 functionality
- Update wallet connection to work with new header but only BackPack can be used right now
- Ensure all blockchain calls remain functional
- Add network switching capability (Gorbagana only for now but I want to be able to quickly support the Solana Bridge when its ready)

#### 6.2 State Management
- Move pool state to context/provider level
- Implement global loading states
- Add error boundary components
- Handle wallet connection state across components

## File Structure Changes

### New Files to Create:
```
src/
├── components/
│   ├── layout/
│   │   ├── Header.tsx
│   │   ├── Sidebar.tsx
│   │   ├── Footer.tsx
│   │   └── MainLayout.tsx
│   ├── modals/
│   │   ├── SettingsModal.tsx
│   │   └── CreatePoolModal.tsx
│   └── common/
│       ├── TokenSelector.tsx
│       ├── LoadingSpinner.tsx
│       └── ErrorBoundary.tsx
├── contexts/
│   ├── PoolContext.tsx
│   └── UIContext.tsx
└── styles/
    ├── components.css
    └── animations.css
```

### Files to Modify:
- `src/components/TrashSwap.tsx` - Major restructure
- `src/components/SwapTokens.tsx` - UI enhancements
- `src/components/PoolList.tsx` - Sidebar adaptation
- `src/components/CreatePool.tsx` - Convert to modal
- `src/app/globals.css` - Add new styles
- `src/app/layout.tsx` - Integrate new layout

## Implementation Priority

### Phase 1: Core Layout (Week 1)
1. Create MainLayout component
2. Build Header with wallet integration
3. Implement Sidebar structure
4. Update TrashSwap.tsx to use new layout

### Phase 2: Component Enhancement (Week 2)
1. Enhance SwapTokens interface
2. Adapt PoolList for sidebar
3. Create modals for settings and pool creation
4. Add responsive design

### Phase 3: Polish and Optimization (Week 3)
1. Implement animations and transitions
2. Add loading states and error handling
3. Optimize for mobile devices
4. Performance testing and optimization

## Success Criteria

### Functional Requirements
- ✅ All existing DEX functionality preserved
- ✅ Wallet connection works seamlessly
- ✅ Pool creation and management functional
- ✅ Token swapping operates correctly
- ✅ Responsive design works on all devices

### Design Requirements
- ✅ Matches the sleek, modern aesthetic of the reference image
- ✅ Sidebar displays pools effectively
- ✅ Main swap interface is prominent and user-friendly
- ✅ Header contains all necessary controls
- ✅ Footer provides easy access to social links

### Performance Requirements
- ✅ Fast loading times
- ✅ Smooth animations (60fps)
- ✅ Efficient re-renders
- ✅ Optimized bundle size

## Additional Considerations

### Accessibility
- Implement proper ARIA labels
- Ensure keyboard navigation works
- Maintain sufficient color contrast
- Add screen reader support

### SEO and Meta Tags
- Update page titles and descriptions
- Add proper Open Graph tags
- Implement structured data for DeFi protocols

### Analytics Integration
- Track user interactions with new layout
- Monitor conversion rates for swaps
- Analyze pool creation patterns

### Future Enhancements
- Add advanced trading features (limit orders, etc.)
- Implement portfolio tracking
- Add yield farming opportunities
- Create governance token integration

---

## Notes for Implementation

1. **Maintain TrashSwap Brand**: Keep the "trashy" theme while making it professional
2. **Gorbagana Integration**: Ensure all network-specific features work correctly
3. **Performance First**: Prioritize smooth user experience over flashy effects
4. **Mobile-First**: Design for mobile, then enhance for desktop
5. **Accessibility**: Make sure the app is usable by everyone

This restructure will transform TrashSwap from a functional DEX into a professional, user-friendly platform that can compete with major DeFi protocols while maintaining its unique trashy personality.